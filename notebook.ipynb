# Import necessary libraries
import pandas as pd

# Load the dataset
ds_jobs = pd.read_csv("customer_train.csv")

# View the dataset
ds_jobs.head()

# Create a copy of ds_jobs for transforming
ds_jobs_transformed = ds_jobs.copy()
ds_jobs_transformed.info()

# Convert columns to appropriate data types
# Columns containing categories with only two factors must be stored as Booleans (bool).
ds_jobs_transformed["is_male"] = ds_jobs_transformed["gender"].map({"Male": True, "Female": False})
ds_jobs_transformed.drop(columns=["gender"], inplace=True)
ds_jobs_transformed["is_male"] = ds_jobs_transformed["is_male"].astype(bool)

ds_jobs_transformed["is_experienced"] = ds_jobs_transformed["relevant_experience"].map({"Has relevant experience": True, "No relevant experience": False})
ds_jobs_transformed.drop(columns=["relevant_experience"], inplace=True)
ds_jobs_transformed["is_experienced"] = ds_jobs_transformed["is_experienced"].astype(bool)

ds_jobs_transformed.info()


ds_jobs_transformed["student_id"] = ds_jobs_transformed["student_id"].astype("int32")
ds_jobs_transformed["training_hours"] = ds_jobs_transformed["training_hours"].astype("int32")
ds_jobs_transformed["job_change"] = ds_jobs_transformed["job_change"].astype("int32")
ds_jobs_transformed["city_development_index"] = ds_jobs_transformed["city_development_index"].astype("float16")
ds_jobs_transformed.info()

ds_jobs_transformed["enrolled_university"] = ds_jobs_transformed["enrolled_university"].astype("category")
ds_jobs_transformed["enrolled_university"] = ds_jobs_transformed["enrolled_university"].cat.reorder_categories(["no_enrollment","Part time course","Full time course"], ordered=True)

ds_jobs_transformed["education_level"] = ds_jobs_transformed["education_level"].astype("category")
ds_jobs_transformed["education_level"] = ds_jobs_transformed["education_level"].cat.reorder_categories(["Primary School","High School","Graduate","Masters","Phd"], ordered=True)

ds_jobs_transformed["experience"] = ds_jobs_transformed["experience"].astype("category")
ds_jobs_transformed["experience"].cat.reorder_categories(["<1","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",">20"], ordered=True)

ds_jobs_transformed["company_size"] = ds_jobs_transformed["company_size"].astype("category")
ds_jobs_transformed["company_size"] = ds_jobs_transformed["company_size"].cat.reorder_categories(["<10","10-49","50-99","100-499","500-999","1000-4999","5000-9999","10000+"], ordered=True)

ds_jobs_transformed["last_new_job"] = ds_jobs_transformed["last_new_job"].astype("category")
ds_jobs_transformed["last_new_job"] = ds_jobs_transformed["last_new_job"].cat.reorder_categories(["never","1","2","3","4",">4"], ordered=True)

ds_jobs_transformed.info()

ds_jobs_transformed["city"] = ds_jobs_transformed["city"].astype("category")
ds_jobs_transformed["major_discipline"] = ds_jobs_transformed["major_discipline"].astype("category")
ds_jobs_transformed["company_type"] = ds_jobs_transformed["company_type"].astype("category")
ds_jobs_transformed.info()

ds_jobs.info()