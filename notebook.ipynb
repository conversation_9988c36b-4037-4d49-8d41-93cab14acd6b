# Import necessary libraries
import pandas as pd

# Load the dataset
ds_jobs = pd.read_csv("customer_train.csv")

# View the dataset
ds_jobs.head()

# Create a copy of ds_jobs for transforming
ds_jobs_transformed = ds_jobs.copy()
ds_jobs_transformed.info()

# Convert columns to appropriate data types
# Columns containing categories with only two factors must be stored as Booleans (bool).
# Keep original column names but convert to boolean
ds_jobs_transformed["gender"] = ds_jobs_transformed["gender"].map({"Male": True, "Female": False})
ds_jobs_transformed["gender"] = ds_jobs_transformed["gender"].astype(bool)

ds_jobs_transformed["relevant_experience"] = ds_jobs_transformed["relevant_experience"].map({"Has relevant experience": True, "No relevant experience": False})
ds_jobs_transformed["relevant_experience"] = ds_jobs_transformed["relevant_experience"].astype(bool)

ds_jobs_transformed.info()

# Convert integer and float columns to appropriate data types
# Columns containing integers only must be stored as 32-bit integers (int32)
ds_jobs_transformed["student_id"] = ds_jobs_transformed["student_id"].astype("int32")
ds_jobs_transformed["training_hours"] = ds_jobs_transformed["training_hours"].astype("int32")

# Columns containing floats must be stored as 16-bit floats (float16)
ds_jobs_transformed["city_development_index"] = ds_jobs_transformed["city_development_index"].astype("float16")

# Convert job_change to boolean (0/1 values)
ds_jobs_transformed["job_change"] = ds_jobs_transformed["job_change"].astype(bool)

ds_jobs_transformed.info()

# Convert categorical columns
# Nominal categorical data (no natural order)
ds_jobs_transformed["city"] = ds_jobs_transformed["city"].astype("category")
ds_jobs_transformed["major_discipline"] = ds_jobs_transformed["major_discipline"].astype("category")
ds_jobs_transformed["company_type"] = ds_jobs_transformed["company_type"].astype("category")

# Ordinal categorical data (with natural order)
ds_jobs_transformed["enrolled_university"] = ds_jobs_transformed["enrolled_university"].astype("category")
ds_jobs_transformed["enrolled_university"] = ds_jobs_transformed["enrolled_university"].cat.reorder_categories(["no_enrollment","Part time course","Full time course"], ordered=True)

ds_jobs_transformed["education_level"] = ds_jobs_transformed["education_level"].astype("category")
ds_jobs_transformed["education_level"] = ds_jobs_transformed["education_level"].cat.reorder_categories(["Primary School","High School","Graduate","Masters","Phd"], ordered=True)

ds_jobs_transformed["experience"] = ds_jobs_transformed["experience"].astype("category")
ds_jobs_transformed["experience"] = ds_jobs_transformed["experience"].cat.reorder_categories(["<1","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15","16","17","18","19","20",">20"], ordered=True)

ds_jobs_transformed["company_size"] = ds_jobs_transformed["company_size"].astype("category")
ds_jobs_transformed["company_size"] = ds_jobs_transformed["company_size"].cat.reorder_categories(["<10","10-49","50-99","100-499","500-999","1000-4999","5000-9999","10000+"], ordered=True)

ds_jobs_transformed["last_new_job"] = ds_jobs_transformed["last_new_job"].astype("category")
ds_jobs_transformed["last_new_job"] = ds_jobs_transformed["last_new_job"].cat.reorder_categories(["never","1","2","3","4",">4"], ordered=True)

ds_jobs_transformed.info()

# Apply the filtering requirement: students with 10+ years experience at companies with 1000+ employees
print(f"Original dataset size: {len(ds_jobs_transformed)}")

# Filter for 10+ years of experience (10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, >20)
experience_filter = ds_jobs_transformed['experience'].isin(['10','11','12','13','14','15','16','17','18','19','20','>20'])

# Filter for companies with 1000+ employees (1000-4999, 5000-9999, 10000+)
company_size_filter = ds_jobs_transformed['company_size'].isin(['1000-4999', '5000-9999', '10000+'])

# Apply both filters
ds_jobs_transformed = ds_jobs_transformed[experience_filter & company_size_filter].copy()

print(f"Filtered dataset size: {len(ds_jobs_transformed)}")
print(f"Remaining experience values: {sorted(ds_jobs_transformed['experience'].dropna().unique())}")
print(f"Remaining company sizes: {sorted(ds_jobs_transformed['company_size'].dropna().unique())}")

# Final check of the transformed dataset
print("=== ORIGINAL DATASET ===")
ds_jobs.info()
print("\n=== TRANSFORMED DATASET ===")
ds_jobs_transformed.info()

print("\n=== MEMORY USAGE COMPARISON ===")
print(f"Original memory usage: {ds_jobs.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print(f"Transformed memory usage: {ds_jobs_transformed.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
reduction = (1 - ds_jobs_transformed.memory_usage(deep=True).sum() / ds_jobs.memory_usage(deep=True).sum()) * 100
print(f"Memory reduction: {reduction:.1f}%")