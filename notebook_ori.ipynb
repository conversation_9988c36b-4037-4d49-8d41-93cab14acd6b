{"cells": [{"cell_type": "markdown", "id": "a09e6d54-bf3f-4632-b299-bd49d5c80d5f", "metadata": {}, "source": ["![Two data scientists working on a dashboard.](hr-image-small.png)\n", "\n", "A common problem when creating models to generate business value from data is that the datasets can be so large that it can take days for the model to generate predictions. Ensuring that your dataset is stored as efficiently as possible is crucial for allowing these models to run on a more reasonable timescale without having to reduce the size of the dataset.\n", "\n", "You've been hired by a major online data science training provider called *Training Data Ltd.* to clean up one of their largest customer datasets. This dataset will eventually be used to predict whether their students are looking for a new job or not, information that they will then use to direct them to prospective recruiters.\n", "\n", "You've been given access to `customer_train.csv`, which is a subset of their entire customer dataset, so you can create a proof-of-concept of a much more efficient storage solution. The dataset contains anonymized student information, and whether they were looking for a new job or not during training:\n", "\n", "| Column                   | Description                                                                      |\n", "|------------------------- |--------------------------------------------------------------------------------- |\n", "| `student_id`             | A unique ID for each student.                                                    |\n", "| `city`                   | A code for the city the student lives in.                                        |\n", "| `city_development_index` | A scaled development index for the city.                                         |\n", "| `gender`                 | The student's gender.                                                            |\n", "| `relevant_experience`    | An indicator of the student's work relevant experience.                          |\n", "| `enrolled_university`    | The type of university course enrolled in (if any).                              |\n", "| `education_level`        | The student's education level.                                                   |\n", "| `major_discipline`       | The educational discipline of the student.                                       |\n", "| `experience`             | The student's total work experience (in years).                                  |\n", "| `company_size`           | The number of employees at the student's current employer.                       |\n", "| `company_type`           | The type of company employing the student.                                       |\n", "| `last_new_job`           | The number of years between the student's current and previous jobs.             |\n", "| `training_hours`         | The number of hours of training completed.                                       |\n", "| `job_change`             | An indicator of whether the student is looking for a new job (`1`) or not (`0`). |"]}, {"cell_type": "markdown", "id": "a01fa423", "metadata": {}, "source": ["The Head Data Scientist at Training Data Ltd. has asked you to create a DataFrame called ds_jobs_transformed that stores the data in customer_train.csv much more efficiently. Specifically, they have set the following requirements:\n", "\n", "- Columns containing categories with only two factors must be stored as Booleans (bool).\n", "- Columns containing integers only must be stored as 32-bit integers (int32).\n", "- Columns containing floats must be stored as 16-bit floats (float16).\n", "- Columns containing nominal categorical data must be stored as the category data type.\n", "- Columns containing ordinal categorical data must be stored as ordered categories, and not mapped to numerical values, with an order that reflects the natural order of the column.\n", "- The DataFrame should be filtered to only contain students with 10 or more years of experience at companies with at least 1000 employees, as their recruiter base is suited to more experienced professionals at enterprise companies.\n", "\n", "If you call .info() or .memory_usage() methods on ds_jobs and ds_jobs_transformed after you've preprocessed it, you should notice a substantial decrease in memory usage."]}, {"cell_type": "code", "execution_count": 1, "id": "eda9c1b2-4e9a-41ac-8da6-d4c98775f986", "metadata": {"executionCancelledAt": null, "executionTime": null, "lastExecutedAt": null, "lastExecutedByKernel": null, "lastScheduledRunId": null, "lastSuccessfullyExecutedCode": null, "outputsMetadata": {"0": {"height": 550, "tableState": {}, "type": "dataFrame"}}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>student_id</th>\n", "      <th>city</th>\n", "      <th>city_development_index</th>\n", "      <th>gender</th>\n", "      <th>relevant_experience</th>\n", "      <th>enrolled_university</th>\n", "      <th>education_level</th>\n", "      <th>major_discipline</th>\n", "      <th>experience</th>\n", "      <th>company_size</th>\n", "      <th>company_type</th>\n", "      <th>last_new_job</th>\n", "      <th>training_hours</th>\n", "      <th>job_change</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8949</td>\n", "      <td>city_103</td>\n", "      <td>0.920</td>\n", "      <td>Male</td>\n", "      <td>Has relevant experience</td>\n", "      <td>no_enrollment</td>\n", "      <td>Graduate</td>\n", "      <td>STEM</td>\n", "      <td>&gt;20</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>36</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29725</td>\n", "      <td>city_40</td>\n", "      <td>0.776</td>\n", "      <td>Male</td>\n", "      <td>No relevant experience</td>\n", "      <td>no_enrollment</td>\n", "      <td>Graduate</td>\n", "      <td>STEM</td>\n", "      <td>15</td>\n", "      <td>50-99</td>\n", "      <td>Pvt Ltd</td>\n", "      <td>&gt;4</td>\n", "      <td>47</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11561</td>\n", "      <td>city_21</td>\n", "      <td>0.624</td>\n", "      <td>NaN</td>\n", "      <td>No relevant experience</td>\n", "      <td>Full time course</td>\n", "      <td>Graduate</td>\n", "      <td>STEM</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>never</td>\n", "      <td>83</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>33241</td>\n", "      <td>city_115</td>\n", "      <td>0.789</td>\n", "      <td>NaN</td>\n", "      <td>No relevant experience</td>\n", "      <td>NaN</td>\n", "      <td>Graduate</td>\n", "      <td>Business Degree</td>\n", "      <td>&lt;1</td>\n", "      <td>NaN</td>\n", "      <td>Pvt Ltd</td>\n", "      <td>never</td>\n", "      <td>52</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>666</td>\n", "      <td>city_162</td>\n", "      <td>0.767</td>\n", "      <td>Male</td>\n", "      <td>Has relevant experience</td>\n", "      <td>no_enrollment</td>\n", "      <td>Masters</td>\n", "      <td>STEM</td>\n", "      <td>&gt;20</td>\n", "      <td>50-99</td>\n", "      <td>Funded Startup</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   student_id      city  city_development_index gender  \\\n", "0        8949  city_103                   0.920   Male   \n", "1       29725   city_40                   0.776   Male   \n", "2       11561   city_21                   0.624    NaN   \n", "3       33241  city_115                   0.789    NaN   \n", "4         666  city_162                   0.767   Male   \n", "\n", "       relevant_experience enrolled_university education_level  \\\n", "0  Has relevant experience       no_enrollment        Graduate   \n", "1   No relevant experience       no_enrollment        Graduate   \n", "2   No relevant experience    Full time course        Graduate   \n", "3   No relevant experience                 NaN        Graduate   \n", "4  Has relevant experience       no_enrollment         Masters   \n", "\n", "  major_discipline experience company_size    company_type last_new_job  \\\n", "0             STEM        >20          NaN             NaN            1   \n", "1             STEM         15        50-99         Pvt Ltd           >4   \n", "2             STEM          5          NaN             NaN        never   \n", "3  Business Degree         <1          NaN         Pvt Ltd        never   \n", "4             STEM        >20        50-99  Funded Startup            4   \n", "\n", "   training_hours  job_change  \n", "0              36         1.0  \n", "1              47         0.0  \n", "2              83         0.0  \n", "3              52         1.0  \n", "4               8         0.0  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "\n", "# Load the dataset\n", "ds_jobs = pd.read_csv(\"customer_train.csv\")\n", "\n", "# View the dataset\n", "ds_jobs.head()"]}, {"cell_type": "code", "execution_count": 2, "id": "13757e62-aca2-48f4-b569-a838af11bdd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype  \n", "---  ------                  --------------  -----  \n", " 0   student_id              19158 non-null  int64  \n", " 1   city                    19158 non-null  object \n", " 2   city_development_index  19158 non-null  float64\n", " 3   gender                  14650 non-null  object \n", " 4   relevant_experience     19158 non-null  object \n", " 5   enrolled_university     18772 non-null  object \n", " 6   education_level         18698 non-null  object \n", " 7   major_discipline        16345 non-null  object \n", " 8   experience              19093 non-null  object \n", " 9   company_size            13220 non-null  object \n", " 10  company_type            13018 non-null  object \n", " 11  last_new_job            18735 non-null  object \n", " 12  training_hours          19158 non-null  int64  \n", " 13  job_change              19158 non-null  float64\n", "dtypes: float64(2), int64(2), object(10)\n", "memory usage: 2.0+ MB\n"]}], "source": ["# Create a copy of ds_jobs for transforming\n", "ds_jobs_transformed = ds_jobs.copy()\n", "ds_jobs_transformed.info()"]}, {"cell_type": "code", "execution_count": 3, "id": "9f2958ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype  \n", "---  ------                  --------------  -----  \n", " 0   student_id              19158 non-null  int64  \n", " 1   city                    19158 non-null  object \n", " 2   city_development_index  19158 non-null  float64\n", " 3   enrolled_university     18772 non-null  object \n", " 4   education_level         18698 non-null  object \n", " 5   major_discipline        16345 non-null  object \n", " 6   experience              19093 non-null  object \n", " 7   company_size            13220 non-null  object \n", " 8   company_type            13018 non-null  object \n", " 9   last_new_job            18735 non-null  object \n", " 10  training_hours          19158 non-null  int64  \n", " 11  job_change              19158 non-null  float64\n", " 12  is_male                 19158 non-null  bool   \n", " 13  is_experienced          19158 non-null  bool   \n", "dtypes: bool(2), float64(2), int64(2), object(8)\n", "memory usage: 1.8+ MB\n"]}], "source": ["# Convert columns to appropriate data types\n", "# Columns containing categories with only two factors must be stored as Booleans (bool).\n", "ds_jobs_transformed[\"is_male\"] = ds_jobs_transformed[\"gender\"].map({\"Male\": True, \"Female\": False})\n", "ds_jobs_transformed.drop(columns=[\"gender\"], inplace=True)\n", "ds_jobs_transformed[\"is_male\"] = ds_jobs_transformed[\"is_male\"].astype(bool)\n", "\n", "ds_jobs_transformed[\"is_experienced\"] = ds_jobs_transformed[\"relevant_experience\"].map({\"Has relevant experience\": True, \"No relevant experience\": False})\n", "ds_jobs_transformed.drop(columns=[\"relevant_experience\"], inplace=True)\n", "ds_jobs_transformed[\"is_experienced\"] = ds_jobs_transformed[\"is_experienced\"].astype(bool)\n", "\n", "ds_jobs_transformed.info()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "1746b142", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype  \n", "---  ------                  --------------  -----  \n", " 0   student_id              19158 non-null  int32  \n", " 1   city                    19158 non-null  object \n", " 2   city_development_index  19158 non-null  float16\n", " 3   enrolled_university     18772 non-null  object \n", " 4   education_level         18698 non-null  object \n", " 5   major_discipline        16345 non-null  object \n", " 6   experience              19093 non-null  object \n", " 7   company_size            13220 non-null  object \n", " 8   company_type            13018 non-null  object \n", " 9   last_new_job            18735 non-null  object \n", " 10  training_hours          19158 non-null  int32  \n", " 11  job_change              19158 non-null  int32  \n", " 12  is_male                 19158 non-null  bool   \n", " 13  is_experienced          19158 non-null  bool   \n", "dtypes: bool(2), float16(1), int32(3), object(8)\n", "memory usage: 1.5+ MB\n"]}], "source": ["ds_jobs_transformed[\"student_id\"] = ds_jobs_transformed[\"student_id\"].astype(\"int32\")\n", "ds_jobs_transformed[\"training_hours\"] = ds_jobs_transformed[\"training_hours\"].astype(\"int32\")\n", "ds_jobs_transformed[\"job_change\"] = ds_jobs_transformed[\"job_change\"].astype(\"int32\")\n", "ds_jobs_transformed[\"city_development_index\"] = ds_jobs_transformed[\"city_development_index\"].astype(\"float16\")\n", "ds_jobs_transformed.info()"]}, {"cell_type": "code", "execution_count": 41, "id": "e9139451", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype   \n", "---  ------                  --------------  -----   \n", " 0   student_id              19158 non-null  int32   \n", " 1   city                    19158 non-null  category\n", " 2   city_development_index  19158 non-null  float16 \n", " 3   enrolled_university     18772 non-null  category\n", " 4   education_level         18698 non-null  category\n", " 5   major_discipline        16345 non-null  category\n", " 6   experience              19093 non-null  category\n", " 7   company_size            13220 non-null  category\n", " 8   company_type            13018 non-null  category\n", " 9   last_new_job            18735 non-null  category\n", " 10  training_hours          19158 non-null  int32   \n", " 11  job_change              19158 non-null  int32   \n", " 12  is_male                 19158 non-null  bool    \n", " 13  is_experienced          19158 non-null  bool    \n", "dtypes: bool(2), category(8), float16(1), int32(3)\n", "memory usage: 456.2 KB\n"]}], "source": ["ds_jobs_transformed[\"enrolled_university\"] = ds_jobs_transformed[\"enrolled_university\"].astype(\"category\")\n", "ds_jobs_transformed[\"enrolled_university\"] = ds_jobs_transformed[\"enrolled_university\"].cat.reorder_categories([\"no_enrollment\",\"Part time course\",\"Full time course\"], ordered=True)\n", "\n", "ds_jobs_transformed[\"education_level\"] = ds_jobs_transformed[\"education_level\"].astype(\"category\")\n", "ds_jobs_transformed[\"education_level\"] = ds_jobs_transformed[\"education_level\"].cat.reorder_categories([\"Primary School\",\"High School\",\"Graduate\",\"Masters\",\"Phd\"], ordered=True)\n", "\n", "ds_jobs_transformed[\"experience\"] = ds_jobs_transformed[\"experience\"].astype(\"category\")\n", "ds_jobs_transformed[\"experience\"].cat.reorder_categories([\"<1\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\">20\"], ordered=True)\n", "\n", "ds_jobs_transformed[\"company_size\"] = ds_jobs_transformed[\"company_size\"].astype(\"category\")\n", "ds_jobs_transformed[\"company_size\"] = ds_jobs_transformed[\"company_size\"].cat.reorder_categories([\"<10\",\"10-49\",\"50-99\",\"100-499\",\"500-999\",\"1000-4999\",\"5000-9999\",\"10000+\"], ordered=True)\n", "\n", "ds_jobs_transformed[\"last_new_job\"] = ds_jobs_transformed[\"last_new_job\"].astype(\"category\")\n", "ds_jobs_transformed[\"last_new_job\"] = ds_jobs_transformed[\"last_new_job\"].cat.reorder_categories([\"never\",\"1\",\"2\",\"3\",\"4\",\">4\"], ordered=True)\n", "\n", "ds_jobs_transformed.info()"]}, {"cell_type": "code", "execution_count": 42, "id": "31b9fd35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype   \n", "---  ------                  --------------  -----   \n", " 0   student_id              19158 non-null  int32   \n", " 1   city                    19158 non-null  category\n", " 2   city_development_index  19158 non-null  float16 \n", " 3   enrolled_university     18772 non-null  category\n", " 4   education_level         18698 non-null  category\n", " 5   major_discipline        16345 non-null  category\n", " 6   experience              19093 non-null  category\n", " 7   company_size            13220 non-null  category\n", " 8   company_type            13018 non-null  category\n", " 9   last_new_job            18735 non-null  category\n", " 10  training_hours          19158 non-null  int32   \n", " 11  job_change              19158 non-null  int32   \n", " 12  is_male                 19158 non-null  bool    \n", " 13  is_experienced          19158 non-null  bool    \n", "dtypes: bool(2), category(8), float16(1), int32(3)\n", "memory usage: 456.2 KB\n"]}], "source": ["ds_jobs_transformed[\"city\"] = ds_jobs_transformed[\"city\"].astype(\"category\")\n", "ds_jobs_transformed[\"major_discipline\"] = ds_jobs_transformed[\"major_discipline\"].astype(\"category\")\n", "ds_jobs_transformed[\"company_type\"] = ds_jobs_transformed[\"company_type\"].astype(\"category\")\n", "ds_jobs_transformed.info()"]}, {"cell_type": "code", "execution_count": 43, "id": "28c96ebf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 19158 entries, 0 to 19157\n", "Data columns (total 14 columns):\n", " #   Column                  Non-Null Count  Dtype  \n", "---  ------                  --------------  -----  \n", " 0   student_id              19158 non-null  int64  \n", " 1   city                    19158 non-null  object \n", " 2   city_development_index  19158 non-null  float64\n", " 3   gender                  14650 non-null  object \n", " 4   relevant_experience     19158 non-null  object \n", " 5   enrolled_university     18772 non-null  object \n", " 6   education_level         18698 non-null  object \n", " 7   major_discipline        16345 non-null  object \n", " 8   experience              19093 non-null  object \n", " 9   company_size            13220 non-null  object \n", " 10  company_type            13018 non-null  object \n", " 11  last_new_job            18735 non-null  object \n", " 12  training_hours          19158 non-null  int64  \n", " 13  job_change              19158 non-null  float64\n", "dtypes: float64(2), int64(2), object(10)\n", "memory usage: 2.0+ MB\n"]}], "source": ["ds_jobs.info()"]}], "metadata": {"editor": "DataLab", "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}